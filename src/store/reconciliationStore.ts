import { create } from 'zustand';
import { combine, createJSONStorage, devtools, persist } from 'zustand/middleware';

import { ComparisonKeyMappingType, IProcessorReportMapping, PrimaryKeyMappingType, ReconciliationDataType } from '+types';
import { StoreStorage } from '+utils';

interface IStartReconciliationState {
  startReconciliationData: ReconciliationDataType;
  primaryKeyMappings: PrimaryKeyMappingType;
  comparisonKeyMappings: ComparisonKeyMappingType;
  autoMatchColumns: boolean;
}

interface IStartReconciliationAction {
  setStartReconciliationData: (data: Omit<ReconciliationDataType, 'field_mapping'>) => void;
  clearStartReconciliationData: () => void;
  setPrimaryKeyMappings: (data: PrimaryKeyMappingType) => void;
  setComparisonKeyMappings: (data: ComparisonKeyMappingType) => void;
  updateComparisonKeyMappings: (data: { id: string; value: Partial<IProcessorReportMapping> }) => void;
  deleteComparisonKeyMapping: (id: string) => void;
  clearPrimaryKeyMappings: () => void;
  clearComparisonKeyMappings: () => void;
  setAutoMatchColumns: () => void;
}
const initialState = {
  startReconciliationData: {} as ReconciliationDataType,
  primaryKeyMappings: [] as PrimaryKeyMappingType,
  comparisonKeyMappings: [] as ComparisonKeyMappingType,
  autoMatchColumns: false
};

const useReconciliationStore = create(
  devtools(
    persist(
      combine<IStartReconciliationState, IStartReconciliationAction>(initialState, set => ({
        startReconciliationData: initialState.startReconciliationData,
        setStartReconciliationData: (data: Omit<ReconciliationDataType, 'field_mapping'>) => {
          set(state => ({
            ...state,
            startReconciliationData: {
              ...state.startReconciliationData,
              ...data
            }
          }));
        },
        clearStartReconciliationData: () => {
          set(state => ({
            ...state,
            startReconciliationData: {} as ReconciliationDataType
          }));
        },
        setPrimaryKeyMappings: (data: PrimaryKeyMappingType) => {
          set(state => {
            const existingKeys = new Set(state.primaryKeyMappings.map(mapping => `${mapping.processor_report}:${mapping.internal_report}`));

            const uniqueData = data.filter(mapping => !existingKeys.has(`${mapping.processor_report}:${mapping.internal_report}`));

            return {
              ...state,
              primaryKeyMappings: [...state.primaryKeyMappings, ...uniqueData]
            };
          });
        },
        setComparisonKeyMappings: (data: ComparisonKeyMappingType) => {
          set(state => {
            const existingKeys = new Set(
              state.comparisonKeyMappings.map(mapping => `${mapping.processor_report}:${mapping.internal_report}`)
            );

            const uniqueData = data.filter(mapping => !existingKeys.has(`${mapping.processor_report}:${mapping.internal_report}`));

            return {
              ...state,
              comparisonKeyMappings: [...state.comparisonKeyMappings, ...uniqueData]
            };
          });
        },
        updateComparisonKeyMappings: (data: { id: string; value: Partial<IProcessorReportMapping> }) => {
          set(state => ({
            ...state,
            comparisonKeyMappings: state.comparisonKeyMappings.map(mapping => {
              const updatedMapping = data.id === mapping.id ? { ...mapping, ...data.value } : mapping;
              return updatedMapping;
            })
          }));
        },
        deleteComparisonKeyMapping: (id: string) => {
          set(state => ({
            ...state,
            comparisonKeyMappings: state.comparisonKeyMappings.filter(mapping => mapping.id !== id)
          }));
        },
        clearPrimaryKeyMappings: () => set(state => ({ ...state, primaryKeyMappings: [] })),
        clearComparisonKeyMappings: () => set(state => ({ ...state, comparisonKeyMappings: [] })),
        setAutoMatchColumns: () =>
          set(state => ({
            ...state,
            autoMatchColumns: !state.autoMatchColumns
          }))
      })),
      {
        name: 'reconciliation',
        storage: createJSONStorage(() => StoreStorage)
      }
    )
  )
);

export default useReconciliationStore;
