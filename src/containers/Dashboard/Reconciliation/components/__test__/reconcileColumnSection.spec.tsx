import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import { vi } from 'vitest';

import MockIndex from '+mock/MockIndex';
import { ComparisonKeyMappingType, PrimaryKeyMappingType } from '+types';

import ReconcileColumnSection from '../ReconcileColumnSection';

expect.extend(toHaveNoViolations);

// Mock the helper function
vi.mock('../../helpers/reconcileReportHelper', () => ({
  buildReconciliationReportOptions: vi.fn(() => [
    [
      { label: 'Transaction ID', value: 'transaction_id' },
      { label: 'Amount', value: 'amount' },
      { label: 'Date', value: 'date' }
    ],
    [
      { label: 'Internal ID', value: 'internal_id' },
      { label: 'Payment Amount', value: 'payment_amount' },
      { label: 'Created Date', value: 'created_date' }
    ]
  ])
}));

// Mock the ReconciliationOptionRow component
vi.mock('../ReconciliationOptionRow', () => ({
  default: ({ value, onChange, onDelete }: any) => (
    <div data-testid="reconciliation-option-row">
      <span data-testid="processor-report">{value.processor_report}</span>
      <span data-testid="internal-report">{value.internal_report}</span>
      <button onClick={() => onChange('new_value', 'processor_report')}>Change Processor</button>
      <button onClick={() => onChange('new_internal', 'internal_report')}>Change Internal</button>
      <button onClick={onDelete} data-testid="delete-button">
        Delete
      </button>
    </div>
  )
}));

describe('ReconcileColumnSection', () => {
  const mockPrimaryKeyMappings: PrimaryKeyMappingType = [
    {
      processor_report: 'transaction_id',
      internal_report: 'reference',
      color: '#FF5733'
    },
    {
      processor_report: 'amount',
      internal_report: 'payment_amount',
      color: '#33FF57'
    }
  ];

  const mockComparisonKeyMappings: ComparisonKeyMappingType = [
    {
      id: 'mapping-1',
      processor_report: 'transaction_id',
      internal_report: 'reference'
    },
    {
      id: 'mapping-2',
      processor_report: 'amount',
      internal_report: 'payment_amount'
    }
  ];

  const mockProcessorReportOptions = ['transaction_id', 'amount', 'date', 'status'];

  const defaultProps = {
    comparisonKeyMappings: mockComparisonKeyMappings,
    primaryKeyMappings: mockPrimaryKeyMappings,
    handleComparisonOptionChange: vi.fn(),
    handleDelete: vi.fn(),
    handleAddNewColumn: vi.fn(),
    handleAutoMatchColumns: vi.fn(),
    autoMatchColumns: false,
    removingItems: new Set<string>(),
    handleCancel: vi.fn(),
    handleStartReconciliation: vi.fn(),
    disableStartReconciliationButton: vi.fn(() => false),
    createReconciliation: { isLoading: false },
    displayPreview: false,
    handlePreviewDisplay: vi.fn(),
    processorReportOptions: mockProcessorReportOptions
  };

  const MockedReconcileColumnSection = (props: any) => (
    <MockIndex>
      <ReconcileColumnSection {...defaultProps} {...props} />
    </MockIndex>
  );

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('should be accessible', async () => {
    const { container } = render(<MockedReconcileColumnSection />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('renders component with comparison key mappings', () => {
    render(<MockedReconcileColumnSection />);

    expect(screen.getByText('Columns detected on uploaded report')).toBeInTheDocument();
    expect(screen.getByText('Column to be reconciled on Kora')).toBeInTheDocument();
    expect(screen.getAllByTestId('reconciliation-option-row')).toHaveLength(2);
    expect(screen.getByText('Add new column')).toBeInTheDocument();
  });

  test('renders empty state when no comparison key mappings', () => {
    render(<MockedReconcileColumnSection comparisonKeyMappings={[]} />);

    expect(screen.getByText('No reconciliation columns')).toBeInTheDocument();
    expect(screen.getByText('Add a column to start reconciliation')).toBeInTheDocument();
    expect(screen.queryByText('Columns detected on uploaded report')).not.toBeInTheDocument();
  });

  test('displays switch for auto match columns', () => {
    render(<MockedReconcileColumnSection />);

    expect(screen.getByText('Match columns automatically')).toBeInTheDocument();
    const switchElement = screen.getByRole('switch');
    expect(switchElement).toBeInTheDocument();
    expect(switchElement).not.toBeChecked();
  });

  test('displays checked switch when autoMatchColumns is true', () => {
    render(<MockedReconcileColumnSection autoMatchColumns={true} />);

    const switchElement = screen.getByRole('switch');
    expect(switchElement).toBeChecked();
  });

  test('calls handleAutoMatchColumns when switch is toggled', async () => {
    const user = userEvent.setup();
    const mockHandleAutoMatchColumns = vi.fn();

    render(<MockedReconcileColumnSection handleAutoMatchColumns={mockHandleAutoMatchColumns} />);

    const switchElement = screen.getByRole('switch');
    await user.click(switchElement);

    expect(mockHandleAutoMatchColumns).toHaveBeenCalledWith(true);
  });

  test('calls handleAddNewColumn when add new column button is clicked', async () => {
    const user = userEvent.setup();
    const mockHandleAddNewColumn = vi.fn();

    render(<MockedReconcileColumnSection handleAddNewColumn={mockHandleAddNewColumn} />);

    const addButton = screen.getByText('Add new column');
    await user.click(addButton);

    expect(mockHandleAddNewColumn).toHaveBeenCalledTimes(1);
  });

  test('calls handleCancel when cancel button is clicked', async () => {
    const user = userEvent.setup();
    const mockHandleCancel = vi.fn();

    render(<MockedReconcileColumnSection handleCancel={mockHandleCancel} />);

    const cancelButton = screen.getByText('Cancel');
    await user.click(cancelButton);

    expect(mockHandleCancel).toHaveBeenCalledTimes(1);
  });

  test('calls handleStartReconciliation when start reconciliation button is clicked', async () => {
    const user = userEvent.setup();
    const mockHandleStartReconciliation = vi.fn();

    render(<MockedReconcileColumnSection handleStartReconciliation={mockHandleStartReconciliation} />);

    const startButton = screen.getByText('Start Reconciliation');
    await user.click(startButton);

    expect(mockHandleStartReconciliation).toHaveBeenCalledTimes(1);
  });

  test('disables start reconciliation button when disableStartReconciliationButton returns true', () => {
    const mockDisableStartReconciliationButton = vi.fn(() => true);

    render(<MockedReconcileColumnSection disableStartReconciliationButton={mockDisableStartReconciliationButton} />);

    const startButton = screen.getByText('Start Reconciliation');
    expect(startButton).toBeDisabled();
  });

  test('shows loading spinner when createReconciliation is loading', () => {
    render(<MockedReconcileColumnSection createReconciliation={{ isLoading: true }} />);

    const spinner = document.querySelector('.spinner-border');
    expect(spinner).toBeInTheDocument();
    expect(screen.queryByText('Start Reconciliation')).not.toBeInTheDocument();
  });

  test('applies removing class when item is in removingItems set', () => {
    const removingItems = new Set(['mapping-1']);

    render(<MockedReconcileColumnSection removingItems={removingItems} />);

    const wrappers = screen.getAllByTestId('reconciliation-option-row').map(row => row.parentElement);
    expect(wrappers[0]).toHaveClass('removing');
    expect(wrappers[1]).not.toHaveClass('removing');
  });

  test('calls handleComparisonOptionChange when option is changed', async () => {
    const user = userEvent.setup();
    const mockHandleComparisonOptionChange = vi.fn();

    render(<MockedReconcileColumnSection handleComparisonOptionChange={mockHandleComparisonOptionChange} />);

    const changeButton = screen.getAllByText('Change Processor')[0];
    await user.click(changeButton);

    expect(mockHandleComparisonOptionChange).toHaveBeenCalledWith('new_value', 'processor_report', 'mapping-1');
  });

  test('calls handleDelete when delete button is clicked', async () => {
    const user = userEvent.setup();
    const mockHandleDelete = vi.fn();

    render(<MockedReconcileColumnSection handleDelete={mockHandleDelete} />);

    const deleteButtons = screen.getAllByTestId('delete-button');
    await user.click(deleteButtons[0]);

    expect(mockHandleDelete).toHaveBeenCalledWith('mapping-1');
  });

  test('does not display preview modal when displayPreview is false', () => {
    render(<MockedReconcileColumnSection displayPreview={false} />);

    expect(screen.queryByText('Processor Report Columns')).not.toBeInTheDocument();
  });

  test('displays preview modal when displayPreview is true', () => {
    render(<MockedReconcileColumnSection displayPreview={true} />);

    expect(screen.getByText('Processor Report Columns')).toBeInTheDocument();
    expect(screen.getByText('Close')).toBeInTheDocument();
  });

  test('renders processor report options in preview modal', () => {
    render(<MockedReconcileColumnSection displayPreview={true} />);

    mockProcessorReportOptions.forEach(option => {
      const previewItems = screen.getAllByText(option).filter(item => item.classList.contains('recon-report__preview--item'));
      expect(previewItems.length).toBeGreaterThan(0);
    });
  });

  test('calls handlePreviewDisplay when modal close button is clicked', async () => {
    const user = userEvent.setup();
    const mockHandlePreviewDisplay = vi.fn();

    render(<MockedReconcileColumnSection displayPreview={true} handlePreviewDisplay={mockHandlePreviewDisplay} />);

    const closeButton = screen.getByTestId('close-button');
    await user.click(closeButton);

    expect(mockHandlePreviewDisplay).toHaveBeenCalledTimes(1);
  });

  test('calls handlePreviewDisplay when modal footer close button is clicked', async () => {
    const user = userEvent.setup();
    const mockHandlePreviewDisplay = vi.fn();

    render(<MockedReconcileColumnSection displayPreview={true} handlePreviewDisplay={mockHandlePreviewDisplay} />);

    const footerCloseButton = screen.getAllByText('Close').find(button => button.closest('.modal-footer') !== null);

    if (footerCloseButton) {
      await user.click(footerCloseButton);
      expect(mockHandlePreviewDisplay).toHaveBeenCalledTimes(1);
    }
  });

  test('applies correct background color to preview items based on primaryKeyMappings', () => {
    render(<MockedReconcileColumnSection displayPreview={true} />);

    const previewItems = screen
      .getAllByText(mockProcessorReportOptions[0])
      .filter(item => item.classList.contains('recon-report__preview--item'));

    if (previewItems.length > 0) {
      expect(previewItems[0]).toHaveStyle(`background-color: ${mockPrimaryKeyMappings[0].color}`);
    }
  });

  test('renders correct number of reconciliation option rows', () => {
    render(<MockedReconcileColumnSection />);

    const optionRows = screen.getAllByTestId('reconciliation-option-row');
    expect(optionRows).toHaveLength(mockComparisonKeyMappings.length);
  });

  test('passes correct value to each ReconciliationOptionRow', () => {
    render(<MockedReconcileColumnSection />);

    const processorReports = screen.getAllByTestId('processor-report');
    const internalReports = screen.getAllByTestId('internal-report');

    expect(processorReports[0]).toHaveTextContent('transaction_id');
    expect(internalReports[0]).toHaveTextContent('reference');
    expect(processorReports[1]).toHaveTextContent('amount');
    expect(internalReports[1]).toHaveTextContent('payment_amount');
  });

  test('renders each mapping with unique key', () => {
    render(<MockedReconcileColumnSection />);

    const optionRows = screen.getAllByTestId('reconciliation-option-row');

    expect(optionRows).toHaveLength(2);

    const wrappers = optionRows.map(row => row.parentElement);
    wrappers.forEach(wrapper => {
      expect(wrapper).toHaveClass('reconciliation-option-row-wrapper');
    });
  });
});
