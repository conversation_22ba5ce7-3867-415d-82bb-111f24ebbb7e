import React, { useEffect, useState } from 'react';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { useShallow } from 'zustand/react/shallow';

import useFeedbackHandler from '+hooks/useFeedbackHandler';
import APIRequest from '+services/api-services';
import LoadingPlaceholder from '+shared/LoadingPlaceHolder';
import Typography from '+shared/Typography';
import useReconciliationStore from '+store/reconciliationStore';
import { IProcessorReportMapping, ProcessorConfigDataType, ReconciliationDataType } from '+types';
import { capitalize, capitalizeFirst, history, logError } from '+utils';

import ReconcileColumnSection from '../components/ReconcileColumnSection';
import ReportProfileCard from '../components/ReportProfileCard';
import { generateColor } from '../helpers/reconcileReportHelper';

import './index.scss';

const apiRequest = new APIRequest();

const ReconcileReport = () => {
  const queryClient = useQueryClient();
  const { feedbackInit } = useFeedbackHandler();
  const { startReconciliationData, primaryKeyMappings, comparisonKeyMappings, autoMatchColumns } = useReconciliationStore(
    useShallow(state => ({
      startReconciliationData: state.startReconciliationData,
      primaryKeyMappings: state.primaryKeyMappings,
      comparisonKeyMappings: state.comparisonKeyMappings,
      autoMatchColumns: state.autoMatchColumns
    }))
  );

  const setPrimaryKeyMappings = useReconciliationStore(state => state.setPrimaryKeyMappings);
  const setComparisonKeyMappings = useReconciliationStore(state => state.setComparisonKeyMappings);
  const updateComparisonKeyMappings = useReconciliationStore(state => state.updateComparisonKeyMappings);
  const deleteComparisonKeyMapping = useReconciliationStore(state => state.deleteComparisonKeyMapping);
  const clearStartReconciliationData = useReconciliationStore(state => state.clearStartReconciliationData);
  const setAutoMatchColumns = useReconciliationStore(state => state.setAutoMatchColumns);

  const [internalReportOptions, setInternalReportOptions] = useState<string[]>([]);
  const [processorReportOptions, setProcessorReportOptions] = useState<string[]>([]);
  const [displayPreview, setDisplayPreview] = useState(false);
  const [removingItems, setRemovingItems] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (!startReconciliationData.processor) {
      history.push('/dashboard/reconciliation/start');
    }
  }, []);

  useEffect(() => {
    if (internalReportOptions.length > 0 || processorReportOptions.length > 0) return;
    setInternalReportOptions(primaryKeyMappings.map(mapping => mapping.internal_report));
    setProcessorReportOptions(primaryKeyMappings.map(mapping => mapping.processor_report));
  }, [primaryKeyMappings]);

  const handleMappings = (data: { data: ProcessorConfigDataType[] }) => {
    const actualData = data?.data.filter((item: ProcessorConfigDataType) => item.payment_type === startReconciliationData.payment_type);
    if (actualData.length > 0 && primaryKeyMappings.length === 0 && comparisonKeyMappings.length === 0) {
      const modifiedPrimaryKeyMappings = actualData[0].primary_key_mappings.map((mapping: IProcessorReportMapping, index: number) => ({
        ...mapping,
        color: generateColor(index)
      }));
      setPrimaryKeyMappings(modifiedPrimaryKeyMappings);
      setComparisonKeyMappings(
        actualData[0].comparison_key_mappings.map((mapping: IProcessorReportMapping) => ({
          ...mapping,
          id: crypto.randomUUID()
        }))
      );
    }
  };

  useEffect(() => {
    if (!autoMatchColumns) return;

    comparisonKeyMappings.forEach(mapping => {
      if (mapping.processor_report && !mapping.internal_report) {
        const matchingPrimaryMapping = primaryKeyMappings.find(primary => primary.processor_report === mapping.processor_report);
        if (matchingPrimaryMapping) {
          updateComparisonKeyMappings({
            id: mapping.id,
            value: { internal_report: matchingPrimaryMapping.internal_report }
          });
        }
      } else if (mapping.internal_report && !mapping.processor_report) {
        const matchingPrimaryMapping = primaryKeyMappings.find(primary => primary.internal_report === mapping.internal_report);
        if (matchingPrimaryMapping) {
          updateComparisonKeyMappings({
            id: mapping.id,
            value: { processor_report: matchingPrimaryMapping.processor_report }
          });
        }
      }
    });
  }, [comparisonKeyMappings, autoMatchColumns, primaryKeyMappings, updateComparisonKeyMappings]);

  const { isLoading } = useQuery(
    [`RECONCILIATION_PROCESSOR_CONFIG_${startReconciliationData.processor}_${startReconciliationData.payment_type}`],
    () => apiRequest.getSettlementReconciliationProcessorConfig(startReconciliationData.processor, startReconciliationData.payment_type),

    {
      refetchOnMount: 'always',
      onSuccess(data) {
        handleMappings(data);
      },
      enabled: !!startReconciliationData.processor
    }
  );

  const createReconciliation = useMutation((value: ReconciliationDataType) => apiRequest.createSettlementReconciliation(value), {
    onSuccess: () => {
      queryClient.invalidateQueries('RECONCILIATIONS');
      feedbackInit({
        message: 'Reconciliation created successfully',
        type: 'success'
      });
      clearStartReconciliationData();
      history.push('/dashboard/reconciliation');
    },
    onError: (error: { response: { data: { data: { amount: { message: string } }; message: string } } }) => {
      logError(error.response.data);
      feedbackInit({
        message: error.response.data?.message
          ? `${capitalizeFirst(error.response.data?.message)}`
          : 'There has been an error creating this reconciliation',
        type: 'danger'
      });
    }
  });

  const disableStartReconciliationButton = () => {
    if (comparisonKeyMappings.length === 0) return true;
    if (createReconciliation.isLoading) return true;
    return comparisonKeyMappings.some(mapping => !mapping.internal_report || !mapping.processor_report);
  };
  const handleStartReconciliation = () => {
    createReconciliation.mutate({
      ...startReconciliationData,
      field_mapping: {
        processor: startReconciliationData.processor,
        payment_type: startReconciliationData.payment_type,
        primary_key_mappings: primaryKeyMappings.map(item => ({ ...item, color: undefined })),
        comparison_key_mappings: comparisonKeyMappings.map(item => ({ ...item, id: undefined }))
      }
    });
  };

  const handleAddNewColumn = () => {
    setComparisonKeyMappings([{ processor_report: '', internal_report: '', id: crypto.randomUUID() }]);
  };

  const handleComparisonOptionChange = (value: string | number | (string | number)[], field: keyof IProcessorReportMapping, id: string) => {
    updateComparisonKeyMappings({ value: { [field]: value }, id });
  };

  const handleDelete = (id: string) => {
    setRemovingItems(prev => new Set(prev).add(id));

    setTimeout(() => {
      deleteComparisonKeyMapping(id);
      setRemovingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(id);
        return newSet;
      });
    }, 300);
  };

  const handlePreviewDisplay = () => {
    if (processorReportOptions.length === 0) return;
    setDisplayPreview(!displayPreview);
  };

  const handleCancel = () => {
    clearStartReconciliationData();
    history.push('/dashboard/reconciliation/start');
  };

  const handleAutoMatchColumns = () => {
    setAutoMatchColumns();
  };

  return (
    <section className="recon-report">
      <section className="recon-report__heading">
        <Typography variant="h2" className="recon-report__heading--title">
          Reconcile reports
        </Typography>
        <Typography variant="subtitle4" className="recon-report__heading--description">
          To start the reconciliation process, map each column from the processor’s report to their respective columns on Kora’s internal
          report and examine the discrepancies.
        </Typography>
      </section>
      <section className="recon-report__content">
        <div className="recon-report__content--card">
          <div className="recon-report__content--card__left">
            <ReportProfileCard
              processor={startReconciliationData.processor}
              numberOfColumns={processorReportOptions.length}
              label={capitalize(startReconciliationData.processor?.split('')[0])}
              displayPreview={handlePreviewDisplay}
              hidePreview={processorReportOptions.length === 0}
            />
          </div>
          <div className="recon-report__content--card__right">
            <ReportProfileCard hidePreview numberOfColumns={internalReportOptions.length} label={'K'} />
          </div>
        </div>
        {isLoading ? (
          <div>
            <LoadingPlaceholder type="text" content={3} />
            <LoadingPlaceholder type="text" content={3} />
          </div>
        ) : (
          <ReconcileColumnSection
            comparisonKeyMappings={comparisonKeyMappings}
            primaryKeyMappings={primaryKeyMappings}
            handleComparisonOptionChange={handleComparisonOptionChange}
            handleDelete={handleDelete}
            handleAddNewColumn={handleAddNewColumn}
            disableStartReconciliationButton={disableStartReconciliationButton}
            handleStartReconciliation={handleStartReconciliation}
            handleCancel={handleCancel}
            handleAutoMatchColumns={handleAutoMatchColumns}
            autoMatchColumns={autoMatchColumns}
            removingItems={removingItems}
            processorReportOptions={processorReportOptions}
            displayPreview={displayPreview}
            handlePreviewDisplay={handlePreviewDisplay}
            createReconciliation={createReconciliation}
          />
        )}
      </section>
    </section>
  );
};

export default ReconcileReport;
