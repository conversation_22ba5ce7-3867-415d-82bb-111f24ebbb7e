import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { describe, expect, it } from 'vitest';

import MockIndex from '+mock/MockIndex';

import StartReconciliation from '../index';

const MockedStartReconciliation = () => {
  return (
    <MockIndex>
      <StartReconciliation />
    </MockIndex>
  );
};

describe('StartReconciliation', () => {
  it('should be accessible', async () => {
    const { container } = render(<MockedStartReconciliation />);

    await waitFor(() => {
      expect(screen.getByText('Reconciliation Tool')).toBeInTheDocument();
    });

    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('should render the component with correct content',  () => {
    render(<MockedStartReconciliation />);

    expect(screen.getByText('Reconciliation Tool')).toBeInTheDocument();
    expect(screen.getByText('Fill the form below to start reconciliation process.')).toBeInTheDocument();
    expect(screen.getByText('Go Back')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Continue' })).toBeInTheDocument();
  });

  it('should render form fields correctly', async () => {
    render(<MockedStartReconciliation />);

    await waitFor(() => {
      expect(screen.getByText('Select Processor')).toBeInTheDocument();
    });

    expect(screen.getByText('Payment Type')).toBeInTheDocument();
    expect(screen.getByText('Report Date Range')).toBeInTheDocument();
    expect(screen.getByText('Upload record for reconciliation')).toBeInTheDocument();
  });

  it('should load processor dropdown', async () => {
    render(<MockedStartReconciliation />);

    await waitFor(() => {
      expect(screen.getByLabelText('Select Processor')).toBeInTheDocument();
    });
  });

  it('should handle form interactions', async () => {
    render(<MockedStartReconciliation />);

    await waitFor(() => {
      expect(screen.getByLabelText('Select Processor')).toBeInTheDocument();
    });

    expect(screen.getByText('Pay-ins')).toBeInTheDocument();
  });

  it('should handle file upload', async () => {
    const user = userEvent.setup();
    render(<MockedStartReconciliation />);

    const fileInput = screen.getByLabelText(/upload record for reconciliation/i);
    const file = new File(['test content'], 'test.csv', { type: 'text/csv' });

    await user.upload(fileInput, file);
    expect((fileInput as HTMLInputElement).files?.[0]).toBe(file);
  });

  it('should validate form completion', () => {
    render(<MockedStartReconciliation />);

    const continueButton = screen.getByRole('button', { name: 'Continue' });
    expect(continueButton).toBeDisabled();
  });

  it('should submit reconciliation data to endpoint when form is completed', async () => {
    const user = userEvent.setup();
    render(<MockedStartReconciliation />);

    await waitFor(() => {
      expect(screen.getByLabelText('Select Processor')).toBeInTheDocument();
    });

    const processorSelect = screen.getByLabelText('Select Processor');
    await user.click(processorSelect);

    await waitFor(() => {
      const korapayOption = screen.getByText('Korapay');
      expect(korapayOption).toBeInTheDocument();
    });
    await user.click(screen.getByText('Korapay'));

    const fileInput = screen.getByLabelText(/upload record for reconciliation/i);
    const file = new File(['test,content\n1,data'], 'test.csv', { type: 'text/csv' });
    await user.upload(fileInput, file);

    const dateInput = screen.getByLabelText(/report date range/i);
    expect(dateInput).toBeInTheDocument();

    await user.click(dateInput);
    await user.clear(dateInput);
    await user.type(dateInput, '01/01/2024 - 31/01/2024');

    await waitFor(
      () => {
        const continueButton = screen.getByRole('button', { name: 'Continue' });
        expect(continueButton).not.toBeDisabled();
      },
      { timeout: 5000 }
    );

    const continueButton = screen.getByRole('button', { name: 'Continue' });
    await user.click(continueButton);

    await waitFor(() => {
      expect(continueButton).not.toBeDisabled();
    });
  });

  it('should have maxDate prop configured to prevent future date selection', async () => {
    const user = userEvent.setup();
    render(<MockedStartReconciliation />);

    const dateInput = screen.getByLabelText(/report date range/i);
    expect(dateInput).toBeInTheDocument();

    await user.click(dateInput);

    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 7);
    const futureDateString = futureDate.toLocaleDateString('en-GB');

    await user.clear(dateInput);
    await user.type(dateInput, futureDateString);

    expect(dateInput).toHaveValue(futureDateString);
  });

  it('should accept past and current dates in date picker', async () => {
    const user = userEvent.setup();
    render(<MockedStartReconciliation />);

    const dateInput = screen.getByLabelText(/report date range/i);
    expect(dateInput).toBeInTheDocument();

    const pastDate = new Date();
    pastDate.setDate(pastDate.getDate() - 7);
    const pastDateString = pastDate.toLocaleDateString('en-GB');

    await user.clear(dateInput);
    await user.type(dateInput, pastDateString);

    expect(dateInput).toHaveValue(pastDateString);
  });
});
